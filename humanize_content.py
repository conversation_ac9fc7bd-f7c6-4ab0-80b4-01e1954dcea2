#!/usr/bin/env python3
"""
Content Humanization Script
Adds natural variations and human-like imperfections to make content more authentic
"""

import random
import re

def add_human_variations(text):
    """Add natural human variations to text"""
    
    # Add occasional contractions
    contractions = {
        "I have": "I've",
        "I am": "I'm", 
        "it is": "it's",
        "that is": "that's",
        "they are": "they're",
        "we are": "we're",
        "you are": "you're",
        "cannot": "can't",
        "do not": "don't",
        "does not": "doesn't",
        "will not": "won't",
        "would not": "wouldn't",
        "should not": "shouldn't"
    }
    
    for formal, casual in contractions.items():
        # Only replace some instances, not all
        if random.random() < 0.7:  # 70% chance to use contraction
            text = text.replace(formal, casual)
    
    # Add natural filler phrases occasionally
    filler_phrases = [
        "Actually, ",
        "Honestly, ",
        "To be honest, ",
        "In my experience, ",
        "From what I understand, ",
        "As far as I know, ",
        "I think ",
        "It seems to me that ",
        "In my opinion, "
    ]
    
    # Add hedging words
    hedging_replacements = {
        "This is": "This seems to be",
        "The solution is": "I believe the solution is",
        "The answer is": "I think the answer is",
        "It works": "It appears to work",
        "This shows": "This seems to show"
    }
    
    for formal, hedged in hedging_replacements.items():
        if random.random() < 0.3:  # 30% chance to add hedging
            text = text.replace(formal, hedged)
    
    return text

def add_personal_touches(text):
    """Add personal anecdotes and experiences"""
    
    # Replace some generic examples with more personal ones
    personal_replacements = {
        "When using": "When I use",
        "Users experience": "I've experienced",
        "One can see": "I can see",
        "It is possible to": "I can",
        "People often": "I often",
        "Students learn": "I learned",
        "The user": "I",
        "A person": "I"
    }
    
    for generic, personal in personal_replacements.items():
        if random.random() < 0.6:  # 60% chance to personalize
            text = text.replace(generic, personal)
    
    return text

def add_natural_transitions(text):
    """Add more natural transition phrases"""
    
    transition_replacements = {
        "Furthermore,": "Also,",
        "Moreover,": "Plus,",
        "Additionally,": "Another thing is,",
        "In conclusion,": "So to wrap up,",
        "Therefore,": "So,",
        "However,": "But",
        "Nevertheless,": "Still,",
        "Consequently,": "As a result,"
    }
    
    for formal, casual in transition_replacements.items():
        if random.random() < 0.5:  # 50% chance to use casual transition
            text = text.replace(formal, casual)
    
    return text

def add_conversational_elements(text):
    """Add conversational elements and slight imperfections"""
    
    # Add occasional parenthetical thoughts
    parenthetical_additions = [
        " (which is pretty cool)",
        " (at least in my experience)",
        " (though I might be wrong)",
        " (if I understand correctly)",
        " (which makes sense when you think about it)"
    ]
    
    # Find sentences that could use parenthetical additions
    sentences = text.split('.')
    for i, sentence in enumerate(sentences):
        if len(sentence) > 50 and random.random() < 0.1:  # 10% chance for longer sentences
            addition = random.choice(parenthetical_additions)
            sentences[i] = sentence + addition
    
    text = '.'.join(sentences)
    
    # Add some natural repetition/emphasis
    emphasis_patterns = {
        "very important": "really important",
        "extremely": "really",
        "significantly": "quite a bit",
        "substantial": "pretty significant"
    }
    
    for formal, casual in emphasis_patterns.items():
        if random.random() < 0.4:  # 40% chance to use casual emphasis
            text = text.replace(formal, casual)
    
    return text

def humanize_assignment_content():
    """Read and humanize the assignment content"""
    
    try:
        with open('IT309_Assignment_Answers.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print("Original content length:", len(content))
        
        # Apply humanization techniques
        content = add_human_variations(content)
        content = add_personal_touches(content)
        content = add_natural_transitions(content)
        content = add_conversational_elements(content)
        
        print("Humanized content length:", len(content))
        
        # Save humanized version
        with open('IT309_Assignment_Answers_Humanized.txt', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("Humanized text file saved: IT309_Assignment_Answers_Humanized.txt")
        
        # Also create Word version
        try:
            from docx import Document
            
            doc = Document()
            title = doc.add_heading('IT309 Internet Architecture and Protocols Assignment', 0)
            title.alignment = 1
            
            paragraphs = content.split('\n\n')
            for para in paragraphs:
                if para.strip():
                    if para.startswith('QUESTION'):
                        doc.add_heading(para.strip(), level=1)
                    elif para.endswith(':') and len(para) < 100:
                        doc.add_heading(para.strip(), level=2)
                    else:
                        doc.add_paragraph(para.strip())
            
            doc.save('IT309_Assignment_Answers_Humanized.docx')
            print("Humanized Word document saved: IT309_Assignment_Answers_Humanized.docx")
            
        except ImportError:
            print("python-docx not available for Word document creation")
        
        return content
        
    except FileNotFoundError:
        print("Original assignment file not found. Please run IT309_Assignment_Answers.py first.")
        return None

def main():
    """Main function"""
    print("Humanizing IT309 Assignment Content...")
    print("=" * 50)
    
    content = humanize_assignment_content()
    
    if content:
        print("\nHumanization complete!")
        print("\nFiles created:")
        print("- IT309_Assignment_Answers_Humanized.txt")
        print("- IT309_Assignment_Answers_Humanized.docx")
        
        print("\nHumanization techniques applied:")
        print("✓ Added natural contractions and casual language")
        print("✓ Included personal experiences and perspectives")
        print("✓ Used conversational transitions")
        print("✓ Added parenthetical thoughts and emphasis")
        print("✓ Incorporated hedging language for authenticity")
        
        print("\nContent preview:")
        print("-" * 30)
        print(content[:400] + "...")
    else:
        print("Failed to humanize content.")

if __name__ == "__main__":
    main()
