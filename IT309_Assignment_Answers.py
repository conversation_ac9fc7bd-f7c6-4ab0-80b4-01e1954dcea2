#!/usr/bin/env python3
"""
IT309 Assignment Answer Generator
Creates comprehensive, humanized answers for the IT309 assignment questions
"""

import os
from datetime import datetime

def create_assignment_answers():
    """Create comprehensive answers for IT309 assignment"""
    
    # Assignment content
    content = """
IT309 Internet Architecture and Protocols Assignment
Spring 2025

Student Information:
- Course: IT309 Internet Architecture and Protocols
- Assignment: Spring 2025 Assignment
- Date: {date}

QUESTION 1: OSI Model Analysis

For this analysis, I have chosen the Network Layer (Layer 3) and Transport Layer (Layer 4) of the OSI model, as these layers are fundamental to understanding modern internet communication and demonstrate clear interaction patterns.

a) Real-world Examples of Layer Functions:

Network Layer (Layer 3):
The Network Layer is responsible for routing data packets across different networks and determining the best path for data transmission. In real-world scenarios, this layer is exemplified by:

- Internet Protocol (IP): When you send an email from your computer in Malaysia to a friend in the United States, the Network Layer uses IP addressing to route your message through multiple routers and networks. Each router examines the destination IP address and forwards the packet to the next hop based on routing tables.

- GPS Navigation Systems: Modern GPS devices demonstrate Network Layer functionality by calculating optimal routes between locations, similar to how routers determine the best path for data packets.

- Online Shopping: When you place an order on an e-commerce website, the Network Layer ensures your payment information reaches the correct server by routing packets through the most efficient network path.

Transport Layer (Layer 4):
The Transport Layer ensures reliable data delivery and manages end-to-end communication between applications. Real-world examples include:

- Video Streaming Services: When watching Netflix or YouTube, the Transport Layer (using TCP) ensures video data arrives in the correct order and requests retransmission of any lost packets, providing smooth playback.

- Online Gaming: Multiplayer games often use UDP at the Transport Layer for real-time communication, prioritizing speed over reliability to minimize lag during gameplay.

- File Downloads: When downloading large files, TCP at the Transport Layer breaks the file into segments, ensures all segments arrive correctly, and reassembles them in the proper order.

b) Layer Interaction:

The Network and Transport Layers work together seamlessly to provide reliable internet communication:

- Data Preparation: The Transport Layer receives data from the Session Layer, segments it into manageable chunks, and adds sequence numbers and error-checking information.

- Addressing and Routing: The segmented data is passed to the Network Layer, which adds IP headers containing source and destination addresses and routing information.

- Path Determination: The Network Layer determines the optimal route through the internet infrastructure, while the Transport Layer maintains the connection state and ensures data integrity.

- Error Handling: If the Network Layer encounters routing issues, it communicates with the Transport Layer to handle retransmissions or alternative routing strategies.

c) OSI Model Relevance in Modern Networking:

While the OSI model remains a valuable educational framework, modern networking has evolved beyond its strict layered approach:

Continued Relevance:
- The fundamental concepts of layered networking still apply to modern systems
- Network troubleshooting and design still benefit from the OSI model's structured approach
- Educational institutions continue to use it as a foundation for understanding network concepts

Modern Limitations:
- Cloud computing has blurred traditional layer boundaries, with services spanning multiple layers
- Software-Defined Networking (SDN) allows for more flexible, programmable network architectures
- Microservices and containerization create new networking paradigms not fully captured by the OSI model
- Edge computing and IoT devices require more adaptive networking approaches

The OSI model provides essential foundational knowledge, but modern networks require understanding of hybrid approaches that combine traditional layered concepts with contemporary technologies like virtualization, cloud services, and programmable networks.

QUESTION 2: Packet Switching vs Virtual Circuits

a) Differences in Data Transmission, Reliability, and Efficiency:

Data Transmission:
Packet Switching operates by breaking data into independent packets, each containing destination information and routing independently through the network. Each packet may take different paths to reach the destination, where they are reassembled. This approach provides flexibility but requires more complex reassembly processes.

Virtual Circuits establish a dedicated communication path before data transmission begins. Once established, all data follows the same predetermined route, similar to a traditional telephone connection. This creates a more predictable transmission environment but requires initial setup overhead.

Reliability:
Packet Switching offers inherent redundancy since packets can take alternative routes if network segments fail. However, packets may arrive out of order or be lost, requiring sophisticated error detection and correction mechanisms at higher layers.

Virtual Circuits provide more predictable reliability since the path is established and maintained throughout the communication session. Quality of Service (QoS) can be guaranteed more easily, but the entire communication fails if the established circuit is broken.

Efficiency:
Packet Switching maximizes network resource utilization by allowing multiple communications to share network infrastructure simultaneously. Bandwidth is used only when data is actually being transmitted, making it highly efficient for bursty traffic patterns.

Virtual Circuits can be more efficient for sustained, high-volume communications since they eliminate per-packet routing decisions. However, they may waste resources during idle periods when the circuit is established but not actively transmitting data.

b) Real-world Applications:

Packet Switching Applications:
- Internet Communication: The entire internet infrastructure is built on packet switching principles, allowing billions of users to share network resources efficiently.
- Email Systems: SMTP, POP3, and IMAP protocols use packet switching to deliver messages across diverse network paths.
- Web Browsing: HTTP/HTTPS traffic uses packet switching, enabling web pages to load from multiple servers simultaneously.
- Cloud Services: Amazon Web Services, Microsoft Azure, and Google Cloud Platform rely on packet switching for scalable, distributed computing.

Virtual Circuit Applications:
- Traditional Telephone Networks: PSTN (Public Switched Telephone Network) uses circuit switching to establish dedicated voice connections.
- MPLS Networks: Many enterprise networks use MPLS (Multiprotocol Label Switching) to create virtual circuits for guaranteed performance.
- ATM Networks: Asynchronous Transfer Mode networks in telecommunications use virtual circuits for high-speed data transmission.
- VPN Connections: Some VPN implementations create virtual circuits to ensure secure, dedicated communication paths.

c) Modern Internet Infrastructure and Future Networking:

Packet Switching in Modern Infrastructure:
Packet switching forms the backbone of modern internet and cloud computing infrastructure because it provides:

- Scalability: Supports billions of connected devices without requiring dedicated circuits
- Resilience: Automatic rerouting around network failures ensures continuous connectivity
- Cost-effectiveness: Shared infrastructure reduces overall networking costs
- Flexibility: Supports diverse applications with varying bandwidth and latency requirements

Cloud Computing Benefits:
- Enables elastic scaling of resources based on demand
- Supports microservices architectures with dynamic communication patterns
- Facilitates global content distribution networks (CDNs)
- Allows for efficient resource sharing among multiple tenants

Future Role of Virtual Circuits:
Virtual circuits may experience renewed relevance in future networking scenarios:

- 5G Networks: Network slicing creates virtual circuits for specific applications requiring guaranteed performance
- IoT Applications: Critical IoT systems may benefit from dedicated virtual circuits for reliability
- Edge Computing: Low-latency applications might use virtual circuits to ensure predictable performance
- Autonomous Vehicles: Safety-critical communications may require the guaranteed delivery that virtual circuits provide

The future likely holds a hybrid approach, combining the flexibility of packet switching with the reliability guarantees of virtual circuits, implemented through technologies like Software-Defined Networking (SDN) and Network Function Virtualization (NFV).

Conclusion:
Both packet switching and virtual circuits have their place in modern networking. While packet switching dominates current internet infrastructure due to its efficiency and scalability, virtual circuits continue to serve specialized applications requiring guaranteed performance. The evolution toward hybrid networking approaches suggests that future networks will intelligently combine both paradigms to meet diverse application requirements.

References:
[Note: In a real assignment, this would include proper APA-style citations to academic sources, textbooks, and relevant technical documentation]

Word Count: Approximately 1,200 words
""".format(date=datetime.now().strftime("%B %d, %Y"))
    
    return content

def save_to_txt(content, filename):
    """Save content to text file"""
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(content)
    print(f"Text file saved: {filename}")

def save_to_docx(content, filename):
    """Save content to Word document"""
    try:
        from docx import Document
        from docx.shared import Inches
        
        doc = Document()
        
        # Add title
        title = doc.add_heading('IT309 Internet Architecture and Protocols Assignment', 0)
        title.alignment = 1  # Center alignment
        
        # Split content into paragraphs and add to document
        paragraphs = content.split('\n\n')
        for para in paragraphs:
            if para.strip():
                if para.startswith('QUESTION'):
                    doc.add_heading(para.strip(), level=1)
                elif para.endswith(':') and len(para) < 100:
                    doc.add_heading(para.strip(), level=2)
                else:
                    doc.add_paragraph(para.strip())
        
        doc.save(filename)
        print(f"Word document saved: {filename}")
        
    except ImportError:
        print("python-docx not installed. Installing...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'python-docx'])
        
        # Retry after installation
        from docx import Document
        
        doc = Document()
        title = doc.add_heading('IT309 Internet Architecture and Protocols Assignment', 0)
        title.alignment = 1
        
        paragraphs = content.split('\n\n')
        for para in paragraphs:
            if para.strip():
                if para.startswith('QUESTION'):
                    doc.add_heading(para.strip(), level=1)
                elif para.endswith(':') and len(para) < 100:
                    doc.add_heading(para.strip(), level=2)
                else:
                    doc.add_paragraph(para.strip())
        
        doc.save(filename)
        print(f"Word document saved: {filename}")

def main():
    """Main function to generate assignment answers"""
    print("Generating IT309 Assignment Answers...")
    
    # Create the assignment content
    content = create_assignment_answers()
    
    # Save to both formats
    save_to_txt(content, "IT309_Assignment_Answers.txt")
    save_to_docx(content, "IT309_Assignment_Answers.docx")
    
    print("\nAssignment answers generated successfully!")
    print("Files created:")
    print("- IT309_Assignment_Answers.txt")
    print("- IT309_Assignment_Answers.docx")
    
    print("\nContent preview:")
    print("=" * 50)
    print(content[:500] + "...")

if __name__ == "__main__":
    main()
