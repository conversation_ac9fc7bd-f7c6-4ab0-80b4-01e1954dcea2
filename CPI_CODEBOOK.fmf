[FormFile]
Version=CSPro 7.7
Name=CPI_CODEBOOK_FF
Label=CPI_CODEBOOK
DefaultTextFont=-013 0000 0000 0000 0700 0000 0000 0000 0000 0000 0000 0000 0000 Arial
FieldEntryFont=0018 0000 0000 0000 0600 0000 0000 0000 0000 0000 0000 0000 0000 Courier New
Type=SystemControlled
  
[Dictionaries]
File=.\CPI_CODEBOOK.dcf
  
[Form]
Name=FORM000
Label=(Id Items)
QuestionTextHeight=114
Level=1
Size=890,300
  
Item=OUTLET_UUID
Item=R_TYPE
Item=ENUMERATION_MARKET_EM
  
[EndForm]
  
[Form]
Name=FORM001
Label=CPI_CODEBOOK_COVER Record
QuestionTextHeight=155
Level=1
Size=611,283
  
Item=LGA
Item=DISTRICT_CODE
Item=DISTRICT__NAME
Item=SETTLEMENT
Item=INTRODUCTION
Item=INTERVIEW_RESULT
  
[EndForm]
  
[Form]
Name=FORM002
Label=SECTION A. IDENTIFICATION OF OUTLET
QuestionTextHeight=127
Level=1
Size=34723,630
  
Item=SECTION_A_IDENTIFICATION_OF_OUTLET1000
Item=NAME_OF_OUTLET
  
  
[Text]
Name=TEXT1
Position=301,24,559,40
Text=SECTION A. IDENTIFICATION OF OUTLET
Form=3
 
[EndForm]
  
[Form]
Name=FORM003
Label=SECTION B: CONTACT PERSON’S DETAILS
QuestionTextHeight=95
Level=1
Size=804,524
  
Item=TYPE_OF_OUTLET
Item=PHONE_NUMBER_OF_OUTLET
Item=ND_PHONE_NUMBER_OF_OUTLET
Item=EMAIL_ADDRESS_OF_OUTLET
Item=STREET_ADDRESS_OF_OUTLET
Item=CONTACT_PERSON_NAME
Item=CONTACT_PERSON_EMAIL
Item=POSITION
Item=PHONE_NUMBER_1
Item=PHONE_NUMBER_2
Item=OTHER_SPECIFY_POSITION
  
[EndForm]
  
[Form]
Name=FORM004
Label=METADATA
Level=1
Size=890,300
  
Item=INTERVIEWER_CODE
Item=INTERVIEWER_NAME
Item=INTERVIEW_START_DATE
Item=INTERVIEW_START_TIME
Item=INTERVIEW_END_TIME
  
[EndForm]
  
[Level]
Name=CPI_CODEBOOK_LEVEL
Label=CPI_CODEBOOK Level
  
[Group]
Required=Yes
Name=IDS0_FORM
Label=(Id Items)
Form=1
Max=1
  
[Field]
Name=OUTLET_UUID
Position=255,27,361,47
Item=OUTLET_UUID,CPI_CODEBOOK_DICT
Protected=Yes
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=1
  
[Text]
Position=50,30,123,46
Text=Outlet_uuid
 
  
[Field]
Name=R_TYPE
Position=255,57,270,77
Item=R_TYPE,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
Form=1
  
[Text]
Position=50,60,93,76
Text=R_type
 
  
[Field]
Name=ENUMERATION_MARKET_EM
Position=255,87,284,107
Item=ENUMERATION_MARKET_EM,CPI_CODEBOOK_DICT
DataCaptureType=DropDown
Form=1
  
[Text]
Position=50,90,233,106
Text=ENUMERATION MARKET (EM)
 
[EndGroup]
  
[Group]
Required=Yes
Name=CPI_CODEBOOK_REC_FORM
Label=CPI_CODEBOOK_COVER Record
Form=2
Max=1
  
[Field]
Name=LGA
Position=172,27,187,47
Item=LGA,CPI_CODEBOOK_DICT
Protected=Yes
DataCaptureType=RadioButton
Form=2
  
[Text]
Position=47,30,74,46
Text=LGA
 
  
[Field]
Name=DISTRICT_CODE
Position=172,57,201,77
Item=DISTRICT_CODE,CPI_CODEBOOK_DICT
Protected=Yes
DataCaptureType=DropDown
Form=2
  
[Text]
Position=47,60,147,76
Text=DISTRICT CODE
 
  
[Field]
Name=DISTRICT__NAME
Position=172,87,542,107
Item=DISTRICT__NAME,CPI_CODEBOOK_DICT
Protected=Yes
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=2
  
[Text]
Position=47,90,138,106
Text=District _Name
 
  
[Field]
Name=SETTLEMENT
Position=172,117,215,137
Item=SETTLEMENT,CPI_CODEBOOK_DICT
Protected=Yes
DataCaptureType=TextBox
Form=2
  
[Text]
Position=47,120,132,136
Text=SETTLEMENT
 
  
[Field]
Name=INTRODUCTION
Position=172,147,187,167
Item=INTRODUCTION,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
Form=2
  
[Text]
Position=47,150,145,166
Text=INTRODUCTION
 
  
[Field]
Name=INTERVIEW_RESULT
Position=172,186,187,206
Item=INTERVIEW_RESULT,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
Form=2
  
[Text]
Position=47,189,149,205
Text=Interview Result
 
[EndGroup]
  
[Group]
Required=Yes
Name=SECTION_A_IDENTIFICATION_OF_OUTLET1_FORM
Label=SECTION A. IDENTIFICATION OF OUTLET
Form=3
Max=1
  
[Field]
Name=NAME_OF_OUTLET
Position=199,80,569,100
Item=NAME_OF_OUTLET,CPI_CODEBOOK_DICT
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=3
  
[Text]
Position=28,83,168,99
Text=1.5. NAME OF OUTLET
 
  
[Grid]
Name=SECTION_A_IDENTIFICATION_OF_OUTLET1000
Label=SECTION A. IDENTIFICATION OF OUTLET
Form=3
Required=Yes
Type=Record
TypeName=SECTION_A_IDENTIFICATION_OF_OUTLET1
Max=100
DisplaySize=26,125,32793,605
Orientation=Vertical
ColumnWidth=3458
StubColumnWidth=92
HeadingRowHeight=0
UseOccurrenceLabels=Yes
FreeMovement=No
 
[Text]
Position=3485,0,3500,24
Text=1
 
  
[Text]
Position=3485,0,3500,24
Text=2
 
  
[Text]
Position=3485,0,3500,24
Text=3
 
  
[Text]
Position=3485,0,3500,24
Text=4
 
  
[Text]
Position=3485,0,3500,24
Text=5
 
  
[Text]
Position=3485,0,3500,24
Text=6
 
  
[Text]
Position=3485,0,3500,24
Text=7
 
  
[Text]
Position=3485,0,3500,24
Text=8
 
  
[Text]
Position=3485,0,3500,24
Text=9
 
  
[Text]
Position=3478,0,3500,24
Text=10
 
  
[Text]
Position=3479,0,3500,24
Text=11
 
  
[Text]
Position=3478,0,3500,24
Text=12
 
  
[Text]
Position=3478,0,3500,24
Text=13
 
  
[Text]
Position=3478,0,3500,24
Text=14
 
  
[Text]
Position=3478,0,3500,24
Text=15
 
  
[Text]
Position=3478,0,3500,24
Text=16
 
  
[Text]
Position=3478,0,3500,24
Text=17
 
  
[Text]
Position=3478,0,3500,24
Text=18
 
  
[Text]
Position=3478,0,3500,24
Text=19
 
  
[Text]
Position=3478,0,3500,24
Text=20
 
  
[Text]
Position=3478,0,3500,24
Text=21
 
  
[Text]
Position=3478,0,3500,24
Text=22
 
  
[Text]
Position=3478,0,3500,24
Text=23
 
  
[Text]
Position=3478,0,3500,24
Text=24
 
  
[Text]
Position=3478,0,3500,24
Text=25
 
  
[Text]
Position=3478,0,3500,24
Text=26
 
  
[Text]
Position=3478,0,3500,24
Text=27
 
  
[Text]
Position=3478,0,3500,24
Text=28
 
  
[Text]
Position=3478,0,3500,24
Text=29
 
  
[Text]
Position=3478,0,3500,24
Text=30
 
  
[Text]
Position=3478,0,3500,24
Text=31
 
  
[Text]
Position=3478,0,3500,24
Text=32
 
  
[Text]
Position=3478,0,3500,24
Text=33
 
  
[Text]
Position=3478,0,3500,24
Text=34
 
  
[Text]
Position=3478,0,3500,24
Text=35
 
  
[Text]
Position=3478,0,3500,24
Text=36
 
  
[Text]
Position=3478,0,3500,24
Text=37
 
  
[Text]
Position=3478,0,3500,24
Text=38
 
  
[Text]
Position=3478,0,3500,24
Text=39
 
  
[Text]
Position=3478,0,3500,24
Text=40
 
  
[Text]
Position=3478,0,3500,24
Text=41
 
  
[Text]
Position=3478,0,3500,24
Text=42
 
  
[Text]
Position=3478,0,3500,24
Text=43
 
  
[Text]
Position=3478,0,3500,24
Text=44
 
  
[Text]
Position=3478,0,3500,24
Text=45
 
  
[Text]
Position=3478,0,3500,24
Text=46
 
  
[Text]
Position=3478,0,3500,24
Text=47
 
  
[Text]
Position=3478,0,3500,24
Text=48
 
  
[Text]
Position=3478,0,3500,24
Text=49
 
  
[Text]
Position=3478,0,3500,24
Text=50
 
  
[Text]
Position=3478,0,3500,24
Text=51
 
  
[Text]
Position=3478,0,3500,24
Text=52
 
  
[Text]
Position=3478,0,3500,24
Text=53
 
  
[Text]
Position=3478,0,3500,24
Text=54
 
  
[Text]
Position=3478,0,3500,24
Text=55
 
  
[Text]
Position=3478,0,3500,24
Text=56
 
  
[Text]
Position=3478,0,3500,24
Text=57
 
  
[Text]
Position=3478,0,3500,24
Text=58
 
  
[Text]
Position=3478,0,3500,24
Text=59
 
  
[Text]
Position=3478,0,3500,24
Text=60
 
  
[Text]
Position=3478,0,3500,24
Text=61
 
  
[Text]
Position=3478,0,3500,24
Text=62
 
  
[Text]
Position=3478,0,3500,24
Text=63
 
  
[Text]
Position=3478,0,3500,24
Text=64
 
  
[Text]
Position=3478,0,3500,24
Text=65
 
  
[Text]
Position=3478,0,3500,24
Text=66
 
  
[Text]
Position=3478,0,3500,24
Text=67
 
  
[Text]
Position=3478,0,3500,24
Text=68
 
  
[Text]
Position=3478,0,3500,24
Text=69
 
  
[Text]
Position=3478,0,3500,24
Text=70
 
  
[Text]
Position=3478,0,3500,24
Text=71
 
  
[Text]
Position=3478,0,3500,24
Text=72
 
  
[Text]
Position=3478,0,3500,24
Text=73
 
  
[Text]
Position=3478,0,3500,24
Text=74
 
  
[Text]
Position=3478,0,3500,24
Text=75
 
  
[Text]
Position=3478,0,3500,24
Text=76
 
  
[Text]
Position=3478,0,3500,24
Text=77
 
  
[Text]
Position=3478,0,3500,24
Text=78
 
  
[Text]
Position=3478,0,3500,24
Text=79
 
  
[Text]
Position=3478,0,3500,24
Text=80
 
  
[Text]
Position=3478,0,3500,24
Text=81
 
  
[Text]
Position=3478,0,3500,24
Text=82
 
  
[Text]
Position=3478,0,3500,24
Text=83
 
  
[Text]
Position=3478,0,3500,24
Text=84
 
  
[Text]
Position=3478,0,3500,24
Text=85
 
  
[Text]
Position=3478,0,3500,24
Text=86
 
  
[Text]
Position=3478,0,3500,24
Text=87
 
  
[Text]
Position=3478,0,3500,24
Text=88
 
  
[Text]
Position=3478,0,3500,24
Text=89
 
  
[Text]
Position=3478,0,3500,24
Text=90
 
  
[Text]
Position=3478,0,3500,24
Text=91
 
  
[Text]
Position=3478,0,3500,24
Text=92
 
  
[Text]
Position=3478,0,3500,24
Text=93
 
  
[Text]
Position=3478,0,3500,24
Text=94
 
  
[Text]
Position=3478,0,3500,24
Text=95
 
  
[Text]
Position=3478,0,3500,24
Text=96
 
  
[Text]
Position=3478,0,3500,24
Text=97
 
  
[Text]
Position=3478,0,3500,24
Text=98
 
  
[Text]
Position=3478,0,3500,24
Text=99
 
  
[Text]
Position=3471,0,3500,24
Text=100
 
  
[Column]
Width=10
[EndColumn]
  
[Column]
Width=28
  
[HeaderText]
Position=36,4,92,28
Text=Item_ID
 
[Field]
Name=ITEM_ID
Position=3407,0,3450,20
Item=ITEM_ID,CPI_CODEBOOK_DICT
Protected=Yes
DataCaptureType=TextBox
  
[Text]
Position=0,0,50,16
Text=Item_ID
 
[EndColumn]
  
[Column]
Width=40
  
[HeaderText]
Position=21,0,92,40
Text=1.7 NAME OF ITEM
 
[Field]
Name=NAME_OF_ITEM
Position=0,0,57,20
Item=NAME_OF_ITEM,CPI_CODEBOOK_DICT
DataCaptureType=DropDown
  
[Text]
Position=0,0,122,16
Text=1.7 NAME OF ITEM
 
[EndColumn]
  
[Column]
Width=56
  
[HeaderText]
Position=0,0,92,56
Text=1.8  DISCRIPTION OF ITEM
 
[Field]
Name=DISCRIPTION_OF_ITEM
Position=3407,0,3450,20
Item=DISCRIPTION_OF_ITEM,CPI_CODEBOOK_DICT
Protected=Yes
DataCaptureType=RadioButton
  
[Text]
Position=0,0,174,16
Text=1.8  DISCRIPTION OF ITEM
 
[EndColumn]
  
[Column]
Width=40
  
[HeaderText]
Position=17,0,92,40
Text=1.9 Relevance
 
[Field]
Name=RELEVANCE
Position=3435,0,3450,20
Item=RELEVANCE,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
  
[Text]
Text=1.9 Relevance
 
[EndColumn]
  
[Column]
Width=56
  
[HeaderText]
Position=0,0,92,56
Text=1.10. What is the origine of the item
 
[Field]
Name=IMP_LOCAL
Position=3435,0,3450,20
Item=IMP_LOCAL,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
  
[Text]
Text=1.10. What is the origine of the item
 
[EndColumn]
  
[Column]
Width=56
  
[HeaderText]
Position=11,0,92,56
Text=1.11. COICOP06( item code)
 
[Field]
Name=COICOP06_ITEM_CODE
Position=3407,0,3450,20
Item=COICOP06_ITEM_CODE,CPI_CODEBOOK_DICT
Protected=Yes
DataCaptureType=RadioButton
  
[Text]
Position=0,0,178,16
Text=1.11. COICOP06( item code)
 
[EndColumn]
  
[Column]
Width=72
  
[HeaderText]
Position=0,0,92,72
Text=1.12. What is the unit of meaurment (UoM)
 
[Field]
Name=UOM
Position=3407,0,3450,20
Item=UOM,CPI_CODEBOOK_DICT
Protected=Yes
DataCaptureType=RadioButton
  
[Text]
Position=0,0,273,16
Text=1.12. What is the unit of meaurment (UoM)
 
[EndColumn]
  
[Column]
Width=40
  
[HeaderText]
Position=0,0,92,40
Text=1.13.  What is the quantity
 
[Field]
Name=QTY202101
Position=3351,0,3450,20
Item=QTY202101,CPI_CODEBOOK_DICT
DataCaptureType=TextBox
  
[Text]
Text=1.13.  What is the quantity
 
[EndColumn]
  
[Column]
Width=40
  
[HeaderText]
Position=0,0,92,40
Text=1.14. What is the price
 
[Field]
Name=PRICE202101
Position=3351,0,3492,20
Item=PRICE202101,CPI_CODEBOOK_DICT
DataCaptureType=TextBox
  
[Text]
Text=1.14. What is the price
 
[EndColumn]
  
[Column]
Width=28
  
[HeaderText]
Position=40,4,92,28
Text=rCheck
 
[Field]
Name=RCHECK
Position=3435,0,3450,20
Item=RCHECK,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
  
[Text]
Text=rCheck
 
[EndColumn]
  
[EndGrid]
 
[EndGroup]
  
[Group]
Required=Yes
Name=SECTION_B_CONTACT_PERSON_S_DETAILS1_FORM
Label=SECTION B: CONTACT PERSON’S DETAILS
Form=4
Max=1
  
[Field]
Name=TYPE_OF_OUTLET
Position=346,132,361,152
Item=TYPE_OF_OUTLET,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
Form=4
  
[Text]
Position=81,135,224,151
Text=1.16. TYPE OF OUTLET
 
  
[Field]
Name=PHONE_NUMBER_OF_OUTLET
Position=346,162,445,182
Item=PHONE_NUMBER_OF_OUTLET,CPI_CODEBOOK_DICT
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,165,295,181
Text=1.17. PHONE NUMBER OF OUTLET
 
  
[Field]
Name=ND_PHONE_NUMBER_OF_OUTLET
Position=346,192,445,212
Item=ND_PHONE_NUMBER_OF_OUTLET,CPI_CODEBOOK_DICT
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,195,324,211
Text=1.17. 2ND PHONE NUMBER OF OUTLET
 
  
[Field]
Name=EMAIL_ADDRESS_OF_OUTLET
Position=346,222,716,242
Item=EMAIL_ADDRESS_OF_OUTLET,CPI_CODEBOOK_DICT
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,225,290,241
Text=1.8. EMAIL ADDRESS OF OUTLET
 
  
[Field]
Name=STREET_ADDRESS_OF_OUTLET
Position=346,252,716,272
Item=STREET_ADDRESS_OF_OUTLET,CPI_CODEBOOK_DICT
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,255,296,271
Text=1.9 STREET ADDRESS OF OUTLET
 
  
[Field]
Name=CONTACT_PERSON_NAME
Position=346,282,476,302
Item=CONTACT_PERSON_NAME,CPI_CODEBOOK_DICT
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,285,268,301
Text=2.1. CONTACT PERSON NAME
 
  
[Field]
Name=CONTACT_PERSON_EMAIL
Position=346,312,716,332
Item=CONTACT_PERSON_EMAIL,CPI_CODEBOOK_DICT
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,315,271,331
Text=2.2. CONTACT PERSON EMAIL
 
  
[Field]
Name=POSITION
Position=346,342,361,362
Item=POSITION,CPI_CODEBOOK_DICT
DataCaptureType=RadioButton
Form=4
  
[Text]
Position=81,345,170,361
Text=2.3. POSITION
 
  
[Field]
Name=OTHER_SPECIFY_POSITION
Position=289,369,779,389
Item=OTHER_SPECIFY_POSITION,CPI_CODEBOOK_DICT
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=4
  
[Text]
Position=88,372,259,388
Text=2.3. Other Specify(Position)
 
  
[Field]
Name=PHONE_NUMBER_1
Position=346,408,445,428
Item=PHONE_NUMBER_1,CPI_CODEBOOK_DICT
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,411,213,427
Text=2.4. Phone Number 1
 
  
[Field]
Name=PHONE_NUMBER_2
Position=346,438,445,458
Item=PHONE_NUMBER_2,CPI_CODEBOOK_DICT
DataCaptureType=TextBox
Form=4
  
[Text]
Position=81,441,213,457
Text=2.5. Phone Number 2
 
[EndGroup]
  
[Group]
Required=Yes
Name=METADATA_FORM
Label=METADATA
Form=5
Max=1
  
[Field]
Name=INTERVIEWER_CODE
Position=201,27,267,47
Item=INTERVIEWER_CODE,CPI_CODEBOOK_DICT
Protected=Yes
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=5
  
[Text]
Position=50,30,157,46
Text=Interviewer code
 
  
[Field]
Name=INTERVIEWER_NAME
Position=201,57,611,77
Item=INTERVIEWER_NAME,CPI_CODEBOOK_DICT
Protected=Yes
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=5
  
[Text]
Position=50,60,163,76
Text=Interviewer Name
 
  
[Field]
Name=INTERVIEW_START_DATE
Position=201,87,291,107
Item=INTERVIEW_START_DATE,CPI_CODEBOOK_DICT
Protected=Yes
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=5
  
[Text]
Position=50,90,176,106
Text=Interview Start Date
 
  
[Field]
Name=INTERVIEW_START_TIME
Position=201,117,291,137
Item=INTERVIEW_START_TIME,CPI_CODEBOOK_DICT
Protected=Yes
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=5
  
[Text]
Position=50,120,179,136
Text=Interview Start Time
 
  
[Field]
Name=INTERVIEW_END_TIME
Position=201,147,291,167
Item=INTERVIEW_END_TIME,CPI_CODEBOOK_DICT
Protected=Yes
UseUnicodeTextBox=Yes
DataCaptureType=TextBox
Form=5
  
[Text]
Position=50,150,173,166
Text=Interview End Time
 
[EndGroup]
  
