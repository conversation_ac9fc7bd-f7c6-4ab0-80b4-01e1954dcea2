
--- PAGE 1 ---
 
Page 1 of 7 
 Please read this document in its entirety before attempting your assignment.  
IT309 Assignment ( Spring2025 ) 
QUESTION 1  
 
Choose TWO  layers of the OSI model and discuss the following: - 
a) Provide real -world examples of how they function.  
b) How do these layers interact with each other?  
c) With the rise of cloud computing and modern networking technologies, does the OSI 
model still fully represent how networks function today? Why or why not.  
 
QUESTION 2  
 
Discuss the various perspectives on packet switching  and virtual c ircuits :- 
 
a) How do packet switching and virtual c ircuits differ in terms of data transmission, 
reliability , and efficiency?  
 
b) Where are packet switching and virtual circuits used in real -world networking? 
Provide examples of technologies or protocols t hat rely on each approach.  
 
c) How does packet switching support the infrastructure of the modern internet and 
cloud computing? Could virtual circuits play a role in future networking?  
 
 
 
 
 
Word limit:  1200 words  (for essay -based answers only)  excluding references  
 
Expectations:  
Utilise the rubric on page 2 and 3 t o organise your assignment report and focus on the 
required criteria.  
 

--- PAGE 2 ---
 
Page 2 of 7 
 EXPEC  TATIONS for SPRING 2025 IT309  ASSIGNMENT - Question 1  Rubric  
Criteria  Weight  Excellent  Good  Fair/  Poor/  Unsatisfactory  Max 
Marks  4 3 2 1 0 
• Overview of the two c hosen layers of the 
OSI model  
• Examples of how each function  
• Interaction between the two layers  
• Relevance of OSI mode l for n etwork function 
today.  
• Justification on this.  4 Provided answers 
that clearly met the 
details of the criteria.  Provided answers 
that mostly met the 
details of the criteria  
but it can be 
improved further . Provided answers 
that basically/ 
minimally met the 
details of the 
criteria.  Provided 
answers that 
poorly met the 
details of the 
criteria.  No answer was 
given.  20 
 
30 
20 
10 
 
10 
Proper format:  
• Apply the template provided; Cover Page, 
Student Plagiarism and Ethical AI Usage 
Declaration, Table of Content, proper 
heading and sub -heading, appropriate 
paragraphing, page numbering, APA type 
references, etc.  1 All formats were 
available and all were 
correct.  More than 
five citations & 
references provided 
and all citations 
sources and 
references  according 
to APA style.  Most formats were 
available and most 
were correct.  Five 
citations and 
references provided 
but not according to 
APA style.  
 Most formats were 
available but only 
some were correct.  
Less than five 
citations and 
references 
provided.  
 Some formats 
were available 
but not correct.  
There are only 
references listed, 
no citations 
included.  No format at all.  
There is no 
citation or 
reference 
provided.  10 
Total  5  100 
 
 
 
 
 
 
 
 
 

--- PAGE 3 ---
 
Page 3 of 7 
 EXPEC  TATIONS for SPRING 2025 IT309  ASSIGNMENT - Question 2  Rubric  
Criteria   Excellent   Good   Fair  Poor   Unsatisfactory  
Marks  
4 3 2 1 0 
(a) Discuss  
How do packet switching and virtual c ircuits differ in 
terms of?  
• data transmission,  
• reliability , and  
• efficiency?  Provided in depth answer 
that clearly met details of 
the criteria.  Provided good answer that 
mostly met details of the 
criteria but it can be 
improved further.  Provided limited answer or 
answer that minimally met 
details of the criteria.  Provided vague/no 
answer or answer that 
poorly met details of 
the criteria.  No answer was 
given.  8 
8 
8 
(b) Discuss  
• Where is packet switching and virtual circuits used 
in real -world networking?  
• Provide examples of technologies or protocols t hat 
rely on each approach.   Provided complete answer 
that clearly met ALL of the 
criteria.  Provided good answer that 
MOSTLY met the criteria 
but it can be improved 
further.  Provided LIMITED answer 
or MINIMAL answer that 
met the criteria.  Provided VAGUE /no 
answer or POOR 
answer based on the 
criteria.  No answer was 
given.  12 
12  
(c) Discuss   
• How does packet switch ing support the 
infrastructure of the modern internet and cloud 
computing?  
• Could virtual circuits play a role in future 
networking?  
 Provided complete answer 
that clearly met ALL of the 
criteria.  Provided good answer that 
MOSTLY met the criteria 
but it can be improved 
further.  Provided LIMITED answer 
or MINIMAL answer that 
met the criteria.  Provided VAGUE  /no 
answer or POOR 
answer based on the 
criteria.  No answer was 
given.  20 
20 
Format of writing:  
Apply the template provided; Cover Page, Student 
Plagiarism and Ethical AI Usage Declaration, Table of 
Content, proper heading and sub -heading, appropriate 
paragraphing, page numbering, APA type references, 
etc. All formats were available 
and all were correct. More 
than five citations & 
references provided and 
all citations sources and 
references according to 
APA style.  Most formats were 
available and most were 
correct. Five citations and 
references provided but 
not according to APA style.  
 Most formats were 
available but only some 
were correct. Less than 
five citations and 
references provided.  
 Some formats were 
available but not 
correct. There are only 
references listed, no 
citations included.   
No format at all. 
There is no 
citation or 
reference 
provided.  12 
Total Score       100 
 
 
 
 

--- PAGE 4 ---
 
Page 4 of 7 
 Uploading Instructions  
1. Create a new MS Word file. Before typing your answer,  please add the following 
information on the first page : 
• Student Name:  
• Student ID:  
• Course Name/Code:  
• Assignment Question:  
• Include in your assignment submission, a signed copy of The Student  Plagiarism & 
Ethical AI Declaration  
2. Please upload it as a Word Document only ; do not make a PDF file  and upload it as this 
hamper  grading and giving comments on your work.  
3. Before uploading , you should suffix your name to the file name and the course code. E.g., 
if a student is uploading an IT309 assignment, then the file name should be as follows: 
IT309_FirstName . For example, IT309_Abdul Rahman.docx  
4. Please note that  upon  uploading your assignment file , it is saved in “draft” mode. To 
“submit,” you must press the Submit  button.  If you forget to submit by not clicking the Submit  
button, the file will remain in “draft” mode and not be considered for grading . Hence, note 
this point seriously and proceed accordingly.   
5. Please note that students are allowed to make  only one submission  attempt . As your first 
attempt of submission will be the last, please ensure your  assignment is relevant, correct and 
complete in all aspects.  
 
  

--- PAGE 5 ---
 
Page 5 of 7 
 IOU’S Plagiarism Policy  &  Ethical AI Usage  
All praise is due to Allah, the Lord of the universe and peace and blessing be upon the last messenger 
Muhammad and all those who follow his path with righteousness until the Last Day.  
Please read and understand this policy in detail before attempting any  assignments at IOU.  
Prophet Muhammad (peace be upon him) said, "He who deceives us is not one of us." ( Sahih  Muslim)  
Definition of Plagiarism:   
A common form of deception in studies is plagiarism. Plagiarism is sometimes misunderstood when 
referring  to copying alone; however, plagiarism is much deeper than just copying. It includes quoting 
without proper references, collaboration, and stealing other people's ideas while passing them off as 
one’s own. Plagiarism is the “failure to acknowledge the ideas or  writing of another” or “presentation 
of the ideas or writing of another as one’s own” and should be read to cover intentional and 
unintentional failure to acknowledge the ideas of others. In this context , “others” means any other 
person,  including a stude nt, academic, professional, published author or other resource such as the 
Internet. The I nternational Open University believes that failing to acknowledge the use of the ideas 
of others constitutes an important breach of the institution's Islamic and acad emic values and 
conventions .  
Definition of Ethical AI Usage  
Ethical AI Usage  refers to the responsible and transparent use of artificial intelligence tools (such as 
ChatGPT, Grammarly, paraphrasing tools, or AI research assistants) to support  academic wor k—not 
to replace or falsify it.  
Students may use AI tools for brainstorming, language enhancement, or initial research, but the final 
work must reflect the student’s own understanding , application to context , analysis, and critical 
thinking . 
 
IOU Stance on  Plagiarism & AI Usage   
The I nternational Open  University will not tolerate plagiarism and unethical AI use  in any form, and 
any student caught plagiarizing  or engaging in Unethical AI use  will be penalized. Depending on the 
severity of the transgression of plagiarism and/or unethical AI usage , the penalty could range from a 
reduction of marks to rejection of marks to rejecting  the paper itself.  
With respect to the assignments, the following constitutes plagiarism:  
1) Copying someone else's assignment  
2) Collaboration and working together on an assignment  
3) Copying and pasting entire articles on the internet  
4) Quoting someone else’s work without referencing the author  
5) Using someone else’s ideas or words without referencing them  
To avoid plagiarism, be sure to:  
• Use quotation marks around a quote and cite the source.  

--- PAGE 6 ---
 
Page 6 of 7 
 • Reference all quotations and citations  
• Reword/paraphrase. Don’t write word for word what you have read. First, t ake notes  in your 
own words, then write your assign ment from your notes (not the text).  
For more information on how to avoid plagiarism and how to properly cite a source, visit the following 
websites:  
http://www.collegebo ard.com/student/plan/college -success/10314.html   
http://www.infoplease.com/spot/plagiarism.html   
 
You may also test your understanding of plagiarism by using this link:  
https://ilrb.cf.ac.uk/plagiarism/tutorial/whatis1.html  
 
Unethical AI Usage Examples:  
1. Submitting assignments fully written by AI without critical review or personal input.  
2. Using AI tools to rewrite (paraphrase ) entire sections without understanding the content.  
3. Copying AI -generated research summaries, literature reviews, or reflections without 
acknowledging AI assistance.  
4. Relying on AI for the complete structure, analysis, and content of an assignment.  
 
To Use AI Ethically:  
• Use AI for supportive purposes  only (e.g., clarifying concepts, improving grammar).  
• Critically review and modify AI -generated content  to reflect your own understanding.  
• Clearly declare any AI assistance  you have used, where appropriate.  
• Use AI responsibly to enhance your learning, not to bypass it.  
 
Similarity Indicator   
IOU reserves the right to submit all academic assignments to a plagiarism checker to produce similarity 
ratings for each assignment  and AI -content detectors to produce similarity and AI -generated content 
ratings for each assignment.  The similarity indicator provides a rating of how similar the text in the 
assignment is to resources available on the Internet while the AI -content check assesses the extent to 
which AI tools  were used to generate the work.  
In line with universal academic writing practices, IOU prefers that text be paraphrased in the student’s  
words  and not quoted directly, with the original author of the knowledge being duly cited and 
referenced. Any use of A I tools must also be transparently acknowledged, and students are reminded 
that over -reliance on AI -generated content may undermine academic integrity and the development 
of essential academic skills  

--- PAGE 7 ---
 
Page 7 of 7 
 As University -wide norm, IOU recommends that the similar ity indicator does not exceed 20%.  
Result of Plagiarism  & Unethical AI Usage  
Assignments  a rating higher than or equal to 50% will be automatically rejected and validated for 
plagiarism  and unethical AI usage through  a manual check by the tutorial assistan t or relevant 
academic. The student will be given a grade of 0  and there will be no second chances for submitting 
the assignment.  
Any detected misuse of AI tools, including unacknowledged AI -generated content or over -reliance that 
compromises academic integrity, will be treated with the same seriousness as traditional plagiarism. 
The tutorial assistant or relevant academic will use the combined similarity and AI -content ratings, 
along with academic judgment, to determine the final grade of the submissio n 
The Tutorial Assistant or relevant academic will use the similarity rating to determine  the final grade 
of the submission.  
Remember that we are Muslims studying Allah’s religion, and so plagiarizing in Islamic Studies is a sin 
and can rob your studies o f reward.   
May Allah bless you all and success with your coursework. Ameen.  
IOU Admin.  
 
 
