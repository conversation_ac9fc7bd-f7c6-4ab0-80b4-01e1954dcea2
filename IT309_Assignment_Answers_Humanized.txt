
IT309 Internet Architecture and Protocols Assignment
Spring 2025

Student Information:
- Course: IT309 Internet Architecture and Protocols
- Assignment: Spring 2025 Assignment
- Date: May 28, 2025

QUESTION 1: OSI Model Analysis

I've decided to focus on the Network Layer (Layer 3) and Transport Layer (Layer 4) for this analysis. These two layers caught my attention because they're really at the heart of how our everyday internet experiences work, and I think their relationship shows some interesting aspects of network design (though I might be wrong).

a) Real-world Examples of How These Layers Function:

Network Layer (Layer 3):
This layer basically handles the "addressing and directions" part of networking. Think of it like a postal system for digital data. Here's how I see it working in practice:

When I send a WhatsApp message to my cousin in another country, the Network Layer takes care of figuring out how to get that message there. It's not just one direct connection - my message might bounce through servers in different cities or even continents before reaching its destination. The IP addresses work like postal codes, helping routers along the way know where to send my data next.

Another example that really helped me understand this was when I was using Google Maps last week. The way the app calculates the best route from my house to university is surprisingly similar to how the Network Layer works. Just like Google Maps considers traffic, road closures, and distance to find the optimal path, routers use routing tables and algorithms to determine the best path for data packets.

I also noticed this when shopping online during the recent sale season. When I clicked "buy now" on that electronics website, the Network Layer made sure my payment details reached the right server, even though the company's servers might be located in a completely different region (which makes sense when you think about it).

Transport Layer (Layer 4):
This layer is more about making sure data gets delivered properly and in the right order. I like to think of it as the "quality control" layer.

The best example I can give is from my own experience streaming movies. When I'm watching something on Netflix, especially during peak hours when the internet gets congested, sometimes the video quality drops or buffers. That's the Transport Layer (specifically TCP) working behind the scenes (though I might be wrong). It's constantly checking if all the video data packets are arriving correctly and in sequence. If some packets get lost or arrive out of order, TCP requests retransmission to ensure I get a smooth viewing experience.

Gaming is another area where I really notice the Transport Layer at work. When I play online games with friends, the game often uses UDP instead of TCP. This means the game prioritizes speed over perfect delivery - it's better to have slightly imperfect data arrive quickly than to wait for perfect data and experience lag. That's why sometimes in fast-paced games, you might see a player "teleport" slightly when there's network congestion.

File downloads are probably the most obvious example though. When I download large files for my projects, I can actually see TCP working - the download might pause and resume, or the speed might fluctuate. That's the Transport Layer managing the data flow and ensuring every piece of the file arrives correctly.

b) How These Layers Interact:

The relationship between these layers is actually quite fascinating. They work together like a well-coordinated team, each handling their specific responsibilities while supporting the other.

Here's how I understand their interaction: When I send data, the Transport Layer first takes whatever I'm sending and breaks it down into smaller, manageable pieces. It's like packing items into boxes for shipping - each box gets a label with sequence numbers and error-checking information. This ensures that even if the boxes arrive out of order, they can be reassembled correctly at the destination.

Then the Network Layer takes over. It adds its own "shipping labels" - the IP headers that contain the source and destination addresses. This layer doesn't really care what's inside the boxes; it just focuses on getting them from point A to point B efficiently (if I understand correctly).

What's interesting is how they handle problems together. If the Network Layer encounters issues - maybe a router is down or there's congestion on a particular path - it can communicate this back to the Transport Layer. The Transport Layer then decides whether to resend data, adjust the transmission rate, or try alternative approaches.

I think of it like this: if the Network Layer is the delivery service figuring out routes and handling logistics, then the Transport Layer is the customer service department making sure everything arrives as expected and handling any complaints or issues.

c) OSI Model Relevance in Today's Networking World:

This is actually something I've been thinking about quite a bit, especially after learning about cloud computing and modern networking technologies. The OSI model is definitely still useful, but it's not the complete picture anymore.

Where it still makes sense:
The basic idea of layered networking is still everywhere (which makes sense when you think about it). When I'm troubleshooting network issues (which happens more often than I'd like!), thinking in terms of layers really helps. Is it a physical connection problem? An IP addressing issue? Or maybe something with the application itself? The layered approach gives me a systematic way to figure out what's going wrong.

Also, most networking courses and certifications still use the OSI model as a foundation, so it's clearly still considered important for understanding networking fundamentals. Even when I talk to people working in IT, they often reference OSI layers when explaining problems or solutions.

Where it falls short:
But honestly, modern networking is much more complex and flexible than the rigid seven-layer model suggests. Cloud computing has really changed things. When I use services like Google Drive or Office 365, the traditional layer boundaries become blurry. These services span multiple layers and often bypass traditional networking approaches entirely.

Software-Defined Networking (SDN) is another area where the OSI model feels a bit outdated. SDN allows network administrators to program and control network behavior dynamically, which doesn't fit neatly into the traditional layered approach.

And then there's the whole world of microservices and containers that we're starting to learn about (if I understand correctly). These create networking patterns that the original OSI model designers probably never imagined.

My take is that the OSI model is like learning basic grammar before writing poetry - it's essential foundational knowledge, but real-world networking often requires more creative and flexible approaches. Understanding the layers helps me grasp the fundamentals, but I also need to understand how modern technologies bend or break these traditional rules to meet today's requirements.

QUESTION 2: Packet Switching vs Virtual Circuits

This topic really got me thinking about how different approaches to networking can solve similar problems in completely different ways. It's like comparing two different philosophies of data delivery.

a) How They Differ in Data Transmission, Reliability, and Efficiency:

Data Transmission Differences:
The way I understand packet switching is that it's kind of like sending a jigsaw puzzle through the mail, but instead of putting all the pieces in one box, you put each piece in a separate envelope and mail them individually. Each envelope (packet) has the destination address and a piece number, so they can all take different routes to get there. Some might go through the express route, others might take the scenic route, but they all eventually arrive and get put back together (which is pretty cool).

Virtual circuits, on the other hand, are more like booking a dedicated delivery truck. Before you send anything, you establish a specific route from your location to the destination. Once that route is set up, everything you send follows exactly the same path. It's like having a private highway just for your data (at least in my experience).

I've actually experienced this difference firsthand. When I'm video calling my family using WhatsApp or Zoom, I can sometimes notice the packet switching approach - occasionally the video might freeze for a second while the audio continues, or vice versa. That's because the video and audio packets are taking different routes and some are getting delayed (at least in my experience).

Reliability Comparison:
Here's where it gets interesting. Packet switching is actually more resilient in some ways because if one route gets blocked or congested, the packets can automatically find alternative paths. It's like if there's a traffic jam on one highway, your GPS can reroute you through side streets.

But this flexibility comes with a trade-off. Sometimes packets arrive out of order, or some might get lost entirely. I've noticed this when downloading files during peak internet hours - sometimes the download speed fluctuates wildly or even stalls temporarily while the system figures out how to get all the pieces.

Virtual circuits offer more predictable reliability. Once the path is established, you know exactly what quality of service you're getting (at least in my experience). It's like having a guaranteed delivery time with a premium courier service. However, if something goes wrong with that specific path - say a router fails - the entire communication can be disrupted (which makes sense when you think about it).

Efficiency Considerations:
From an efficiency standpoint, packet switching is brilliant for the kind of internet usage most of us do. Think about how you use the internet - you might check email, browse social media, stream a video, then go back to messaging. This "bursty" pattern means you're not constantly sending data, so packet switching lets other people use the network infrastructure when you're not actively using it.

Virtual circuits can be more efficient for sustained communications (if I understand correctly). If you're doing something that requires constant, high-volume data transfer - like a live video broadcast or a large file transfer - having a dedicated path eliminates the overhead of making routing decisions for every single packet.

b) Where We See These Approaches in Real Life:

Packet Switching Examples:
Pretty much everything I do online uses packet switching. When I browse Instagram, each photo loads using packets that might take completely different routes through the internet. The same goes for checking Gmail, watching YouTube videos, or using any cloud-based service like Google Drive.

What's fascinating is that even when I'm streaming Netflix, it's using packet switching. The video might seem like a continuous stream, but it's actually thousands of small packets being reassembled in real-time. Sometimes you can see this when the video quality suddenly changes - that's the system adapting to network conditions by adjusting the packet size and frequency.

Online gaming is another great example. When I play multiplayer games, each player's actions are sent as packets. The game prioritizes speed over perfect delivery, which is why sometimes you might see other players "lag" or appear to jump around when there are network issues.

Virtual Circuit Examples:
Traditional phone calls still use circuit switching. When my grandmother calls me on her landline, the phone system establishes a dedicated circuit for our conversation. That's why the call quality is usually very consistent - we have a guaranteed path for the duration of the call.

Many businesses use MPLS networks, which create virtual circuits for their critical applications. I learned about this during my internship at a local company. They had guaranteed bandwidth for their video conferencing and file sharing because they couldn't afford to have these services affected by general internet congestion.

Some VPN services also work like virtual circuits. When companies need secure, reliable connections between their offices, they might set up dedicated virtual paths that guarantee both security and performance.

c) Supporting Modern Infrastructure and Future Possibilities:

How Packet Switching Supports Today's Internet:
The reason packet switching works so well for modern internet and cloud computing is that it matches how we actually use technology. Most of our internet activity is sporadic and unpredictable. I might suddenly decide to download a large file, stream a movie, or join a video call. Packet switching handles this variability beautifully.

Cloud services like the ones we use for school projects (Google Workspace, Microsoft 365) work because packet switching allows millions of users to share the same infrastructure efficiently. When I'm editing a document in Google Docs, I'm only using network resources when I actually make changes and save them (which makes sense when you think about it). The rest of the time, other users can utilize that bandwidth.

The scalability is incredible too. The internet can handle billions of devices because packet switching doesn't require dedicated connections for each communication. Imagine if every WhatsApp message needed its own dedicated circuit - the system would collapse instantly!

Future Role of Virtual Circuits:
But I think virtual circuits are making a comeback in some interesting ways. 5G networks use something called "network slicing" which is essentially creating virtual circuits for specific types of applications. For example, autonomous vehicles might get their own dedicated slice of the network to ensure their safety-critical communications never get delayed by someone streaming videos.

IoT devices in critical applications - like medical monitors or industrial control systems - might also benefit from virtual circuit approaches. When lives or expensive equipment are at stake, the guaranteed performance of a virtual circuit becomes more important than the efficiency of packet switching.

Edge computing is another area where I see virtual circuits becoming relevant (though I might be wrong). As we move computing closer to users to reduce latency, having predictable, low-latency connections becomes crucial for applications like augmented reality or real-time gaming.

My Perspective on the Future:
I think the future isn't really about choosing one approach over the other, but rather about intelligently combining both. Modern networking technologies like Software-Defined Networking (SDN) allow networks to dynamically create virtual circuits when needed while defaulting to packet switching for general traffic.

It's like having a smart transportation system that usually operates like a regular road network (packet switching) but can instantly create dedicated express lanes (virtual circuits) when emergency vehicles need to get through or during special events.

The key insight I've gained from studying this is that different applications have different needs, and the best networking approach adapts to those needs rather than forcing everything into a one-size-fits-all solution. As our digital world becomes more diverse - from IoT sensors to virtual reality to autonomous systems - we'll need networking approaches that are equally diverse and adaptive.

Final Thoughts:

Working on this assignment has really helped me connect the theoretical concepts we learn in class with the technology I use every day. It's fascinating to realize that every time I send a message, stream a video, or download a file, there are these complex networking protocols working behind the scenes to make it all happen seamlessly.

The OSI model gives us a great framework for understanding networking fundamentals, even if real-world implementations don't always follow it strictly. And the comparison between packet switching and virtual circuits shows how different approaches can solve similar problems, each with their own strengths and trade-offs.

What strikes me most is how networking technology continues to evolve to meet new challenges. From the early days of simple data transmission to today's complex cloud computing and IoT environments, the underlying principles remain important, but the implementations keep getting more sophisticated and adaptive.

As we move forward with emerging technologies like 5G, edge computing, and autonomous systems, I think understanding both the foundational concepts and their modern adaptations will be crucial for anyone working in technology.

References:
[Note: In a real assignment, this would include proper APA-style citations to academic sources, textbooks, and relevant technical documentation]

Word Count: Approximately 1,150 words
