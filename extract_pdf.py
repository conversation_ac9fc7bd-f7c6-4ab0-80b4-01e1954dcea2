#!/usr/bin/env python3
"""
PDF Text Extraction Script
Extracts text content from the IT309 PDF file
"""

import PyPDF2
import sys
import os

def extract_pdf_text(pdf_path):
    """Extract text from PDF file"""
    try:
        with open(pdf_path, 'rb') as file:
            pdf_reader = PyPDF2.PdfReader(file)
            text_content = ""
            
            print(f"PDF has {len(pdf_reader.pages)} pages")
            
            for page_num, page in enumerate(pdf_reader.pages):
                print(f"Extracting text from page {page_num + 1}...")
                page_text = page.extract_text()
                text_content += f"\n--- PAGE {page_num + 1} ---\n"
                text_content += page_text
                text_content += "\n"
            
            return text_content
    
    except Exception as e:
        print(f"Error extracting PDF: {e}")
        return None

def main():
    pdf_file = "IT309 Internet Architecture and Protocols (IT 309)2025.pdf"
    
    if not os.path.exists(pdf_file):
        print(f"PDF file not found: {pdf_file}")
        return
    
    print(f"Extracting text from: {pdf_file}")
    text_content = extract_pdf_text(pdf_file)
    
    if text_content:
        # Save extracted text to file
        with open("extracted_pdf_content.txt", "w", encoding="utf-8") as f:
            f.write(text_content)
        
        print("Text extracted successfully!")
        print("Content saved to: extracted_pdf_content.txt")
        
        # Display first 2000 characters as preview
        print("\n--- PREVIEW (First 2000 characters) ---")
        print(text_content[:2000])
        print("...")
    else:
        print("Failed to extract text from PDF")

if __name__ == "__main__":
    main()
