<!DOCTYPE html>
<html lang="en" class="light-style" dir="ltr" data-theme="theme-modern" data-assets-path="../assets/" data-template="mobile-template">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes, minimum-scale=1.0, maximum-scale=3.0" />
    <title>CPI Dashboard | Enumerator</title>
    <meta name="description" content="Modern mobile dashboard for CPI data collection - Professional field data management" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="./assets/img/favicon/favicon.ico" />

    <!-- Modern Fonts - Inter for better readability -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet" />

    <!-- Icons -->
    <link rel="stylesheet" href="./assets/vendor/fonts/boxicons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="./assets/vendor/css/core.css" class="template-customizer-core-css" />
    <link rel="stylesheet" href="./assets/vendor/css/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="./assets/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />
    <link rel="stylesheet" href="./assets/vendor/libs/apex-charts/apex-charts.css" />

    <!-- Modern Mobile Dashboard CSS -->
    <style>
        /* CSS Custom Properties for Design System */
        :root {
            /* Modern Color Palette */
            --primary-50: #f0f9ff;
            --primary-100: #e0f2fe;
            --primary-500: #0ea5e9;
            --primary-600: #0284c7;
            --primary-700: #0369a1;
            --primary-900: #0c4a6e;

            --success-50: #f0fdf4;
            --success-500: #22c55e;
            --success-600: #16a34a;

            --warning-50: #fffbeb;
            --warning-500: #f59e0b;
            --warning-600: #d97706;

            --error-50: #fef2f2;
            --error-500: #ef4444;
            --error-600: #dc2626;

            --neutral-50: #f8fafc;
            --neutral-100: #f1f5f9;
            --neutral-200: #e2e8f0;
            --neutral-300: #cbd5e1;
            --neutral-400: #94a3b8;
            --neutral-500: #64748b;
            --neutral-600: #475569;
            --neutral-700: #334155;
            --neutral-800: #1e293b;
            --neutral-900: #0f172a;

            /* Typography */
            --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

            /* Spacing Scale */
            --space-1: 0.25rem;
            --space-2: 0.5rem;
            --space-3: 0.75rem;
            --space-4: 1rem;
            --space-5: 1.25rem;
            --space-6: 1.5rem;
            --space-8: 2rem;
            --space-10: 2.5rem;
            --space-12: 3rem;
            --space-16: 4rem;

            /* Border Radius */
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --radius-xl: 1rem;
            --radius-2xl: 1.5rem;

            /* Shadows */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

            /* Glassmorphism */
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: rgba(255, 255, 255, 0.18);
            --glass-backdrop: blur(16px);
        }

        /* Reset and Base Styles */
        * {
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-family-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
            color: var(--neutral-900);
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Modern Container */
        .dashboard-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: var(--space-4);
            position: relative;
        }

        .dashboard-content {
            max-width: 100%;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        /* Modern Header with Glassmorphism */
        .modern-header {
            background: var(--glass-bg);
            backdrop-filter: var(--glass-backdrop);
            border: 1px solid var(--glass-border);
            border-radius: var(--radius-2xl);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .modern-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
        }

        .header-title {
            font-size: 1.75rem;
            font-weight: 800;
            color: white;
            margin: 0 0 var(--space-2) 0;
            letter-spacing: -0.025em;
        }

        .header-subtitle {
            font-size: 0.875rem;
            color: rgba(255, 255, 255, 0.8);
            margin: 0;
            font-weight: 500;
        }

        /* Modern Statistics Grid */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: var(--space-4);
            margin-bottom: var(--space-8);
        }

        .stat-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--neutral-200);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-color, var(--primary-500));
            border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .stat-card:active {
            transform: translateY(0);
        }

        .stat-card.primary { --accent-color: var(--primary-500); }
        .stat-card.success { --accent-color: var(--success-500); }
        .stat-card.warning { --accent-color: var(--warning-500); }
        .stat-card.error { --accent-color: var(--error-500); }

        .stat-icon {
            width: 48px;
            height: 48px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: var(--space-4);
            font-size: 1.5rem;
        }

        .stat-card.primary .stat-icon {
            background: var(--primary-50);
            color: var(--primary-600);
        }

        .stat-card.success .stat-icon {
            background: var(--success-50);
            color: var(--success-600);
        }

        .stat-card.warning .stat-icon {
            background: var(--warning-50);
            color: var(--warning-600);
        }

        .stat-card.error .stat-icon {
            background: var(--error-50);
            color: var(--error-600);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 800;
            color: var(--neutral-900);
            margin: 0 0 var(--space-1) 0;
            font-family: var(--font-family-mono);
            line-height: 1;
        }

        .stat-label {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--neutral-600);
            margin: 0;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .stat-trend {
            display: flex;
            align-items: center;
            gap: var(--space-1);
            margin-top: var(--space-2);
            font-size: 0.75rem;
            font-weight: 500;
        }

        .trend-up {
            color: var(--success-600);
        }

        .trend-down {
            color: var(--error-600);
        }

        /* Modern Chart Container */
        .chart-container {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-6);
            margin-bottom: var(--space-6);
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--neutral-200);
        }

        .chart-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: var(--space-6);
        }

        .chart-title {
            font-size: 1.125rem;
            font-weight: 700;
            color: var(--neutral-900);
            margin: 0;
        }

        .chart-subtitle {
            font-size: 0.875rem;
            color: var(--neutral-500);
            margin: var(--space-1) 0 0 0;
        }

        /* Modern Case List */
        .case-list {
            background: white;
            border-radius: var(--radius-xl);
            overflow: hidden;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--neutral-200);
            margin-bottom: var(--space-6);
        }

        .case-list-header {
            background: var(--neutral-50);
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--neutral-200);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .case-list-title {
            font-size: 1rem;
            font-weight: 700;
            color: var(--neutral-900);
            margin: 0;
            display: flex;
            align-items: center;
            gap: var(--space-2);
        }

        .case-count {
            background: var(--primary-100);
            color: var(--primary-700);
            padding: var(--space-1) var(--space-2);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            font-family: var(--font-family-mono);
        }

        .case-item {
            padding: var(--space-4) var(--space-6);
            border-bottom: 1px solid var(--neutral-100);
            transition: background-color 0.2s ease;
        }

        .case-item:last-child {
            border-bottom: none;
        }

        .case-item:hover {
            background: var(--neutral-50);
        }

        .case-header {
            display: flex;
            align-items: flex-start;
            justify-content: space-between;
            margin-bottom: var(--space-3);
        }

        .case-name {
            font-size: 1rem;
            font-weight: 600;
            color: var(--neutral-900);
            margin: 0;
            line-height: 1.4;
        }

        .case-details {
            font-size: 0.875rem;
            color: var(--neutral-600);
            margin: var(--space-1) 0 var(--space-3) 0;
            line-height: 1.4;
        }

        .case-meta {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: var(--space-2);
        }

        .case-status {
            padding: var(--space-1) var(--space-3);
            border-radius: var(--radius-md);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .status-completed {
            background: var(--success-50);
            color: var(--success-700);
        }

        .status-partial {
            background: var(--warning-50);
            color: var(--warning-700);
        }

        .status-unavailable,
        .status-not-found {
            background: var(--error-50);
            color: var(--error-700);
        }

        /* Modern Action Button */
        .action-btn {
            background: var(--primary-500);
            color: white;
            border: none;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: var(--space-1);
            text-decoration: none;
        }

        .action-btn:hover {
            background: var(--primary-600);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .action-btn:active {
            transform: translateY(0);
        }

        /* Floating Action Button */
        .fab {
            position: fixed;
            bottom: var(--space-6);
            right: var(--space-6);
            width: 56px;
            height: 56px;
            background: var(--primary-500);
            color: white;
            border: none;
            border-radius: 50%;
            box-shadow: var(--shadow-xl);
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            z-index: 1000;
        }

        .fab:hover {
            background: var(--primary-600);
            transform: scale(1.1);
        }

        .fab:active {
            transform: scale(0.95);
        }

        /* Modern Toast */
        .modern-toast {
            position: fixed;
            top: var(--space-6);
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border: 1px solid var(--warning-200);
            border-left: 4px solid var(--warning-500);
            border-radius: var(--radius-lg);
            padding: var(--space-4);
            box-shadow: var(--shadow-xl);
            z-index: 1001;
            display: none;
            max-width: calc(100vw - 2rem);
            min-width: 300px;
        }

        .toast-content {
            display: flex;
            align-items: flex-start;
            gap: var(--space-3);
        }

        .toast-icon {
            width: 20px;
            height: 20px;
            color: var(--warning-500);
            flex-shrink: 0;
            margin-top: 2px;
        }

        .toast-text {
            flex: 1;
        }

        .toast-title {
            font-size: 0.875rem;
            font-weight: 600;
            color: var(--neutral-900);
            margin: 0 0 var(--space-1) 0;
        }

        .toast-message {
            font-size: 0.875rem;
            color: var(--neutral-600);
            margin: 0;
        }

        .toast-close {
            background: none;
            border: none;
            color: var(--neutral-400);
            cursor: pointer;
            padding: 0;
            width: 20px;
            height: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-sm);
            transition: all 0.2s ease;
        }

        .toast-close:hover {
            background: var(--neutral-100);
            color: var(--neutral-600);
        }

        /* Loading States */
        .loading-spinner {
            width: 20px;
            height: 20px;
            border: 2px solid var(--neutral-200);
            border-top: 2px solid var(--primary-500);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive Design */
        @media (max-width: 640px) {
            .dashboard-container {
                padding: var(--space-3);
            }

            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: var(--space-3);
            }

            .stat-card {
                padding: var(--space-4);
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .case-header {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--space-2);
            }

            .case-meta {
                flex-direction: column;
                align-items: flex-start;
            }

            .fab {
                bottom: var(--space-4);
                right: var(--space-4);
            }
        }

        @media (max-width: 480px) {
            .header-title {
                font-size: 1.5rem;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .modern-toast {
                min-width: auto;
                left: var(--space-4);
                right: var(--space-4);
                transform: none;
            }
        }

        /* Accessibility */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Focus States */
        .action-btn:focus,
        .fab:focus,
        .toast-close:focus {
            outline: 2px solid var(--primary-500);
            outline-offset: 2px;
        }

        /* Dark Mode Support (Future Enhancement) */
        @media (prefers-color-scheme: dark) {
            :root {
                --neutral-50: #0f172a;
                --neutral-100: #1e293b;
                --neutral-200: #334155;
                --neutral-900: #f8fafc;
            }
        }

        /* Modern Animations */
        @keyframes slideInDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes slideOutUp {
            from {
                transform: translateY(0);
                opacity: 1;
            }
            to {
                transform: translateY(-100%);
                opacity: 0;
            }
        }

        @keyframes fadeInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Pull-to-refresh indicator */
        .pull-refresh-indicator {
            position: fixed;
            top: -60px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: var(--primary-500);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            opacity: 0;
            z-index: 1002;
            transition: all 0.3s ease;
        }

        /* Keyboard navigation styles */
        .keyboard-navigation .action-btn:focus,
        .keyboard-navigation .fab:focus,
        .keyboard-navigation .stat-card:focus {
            outline: 3px solid var(--primary-500);
            outline-offset: 2px;
        }

        /* Screen reader only content */
        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* Loading state */
        .dashboard-loaded .stat-card,
        .dashboard-loaded .case-item {
            animation: fadeInUp 0.6s ease forwards;
        }

        .dashboard-loaded .stat-card:nth-child(1) { animation-delay: 0.1s; }
        .dashboard-loaded .stat-card:nth-child(2) { animation-delay: 0.2s; }
        .dashboard-loaded .stat-card:nth-child(3) { animation-delay: 0.3s; }
        .dashboard-loaded .stat-card:nth-child(4) { animation-delay: 0.4s; }
        .dashboard-loaded .stat-card:nth-child(5) { animation-delay: 0.5s; }
        .dashboard-loaded .stat-card:nth-child(6) { animation-delay: 0.6s; }

        .table-header {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
            font-weight: 600;
        }

        .case-item {
            padding: 1rem;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .case-item:last-child {
            border-bottom: none;
        }

        .case-info {
            flex: 1;
        }

        .case-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
            color: #333;
        }

        .case-details {
            font-size: 0.8rem;
            color: #666;
        }

        .case-status {
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 500;
            margin-left: 0.5rem;
        }

        .status-completed { background: #d4edda; color: #155724; }
        .status-partial { background: #fff3cd; color: #856404; }
        .status-unavailable { background: #f8d7da; color: #721c24; }
        .status-not-found { background: #f8d7da; color: #721c24; }

        .action-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem;
            border-radius: 8px;
            margin-left: 0.5rem;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .action-btn:hover {
            background: #0056b3;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            border: none;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            cursor: pointer;
            z-index: 1000;
            transition: transform 0.2s ease;
        }

        .refresh-btn:hover {
            transform: scale(1.1);
        }

        .header-mobile {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            margin: -0.5rem -0.5rem 1rem -0.5rem;
            text-align: center;
        }

        .header-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .header-subtitle {
            font-size: 0.85rem;
            opacity: 0.9;
        }

        /* Chart container for mobile */
        .mobile-chart {
            background: white;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .chart-title {
            font-size: 1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
        }

        /* Toast notification for mobile */
        .mobile-toast {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: #ff6b6b;
            color: white;
            padding: 0.75rem 1rem;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1001;
            display: none;
        }

        /* Loading spinner */
        .loading-spinner {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive adjustments */
        @media (max-width: 576px) {
            .stats-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .stat-number {
                font-size: 1.5rem;
            }

            .case-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            .case-status {
                margin-left: 0;
            }
        }
    </style>

    <!-- Helpers -->
    <script src="./assets/vendor/js/helpers.js"></script>
    <script src="./assets/js/config.js"></script>
</head>

<body>
    <div class="dashboard-container">
        <div class="dashboard-content">
            <!-- Modern Header with Glassmorphism -->
            <div class="modern-header">
                <h1 class="header-title">CPI Enumerator Dashboard</h1>
                <p class="header-subtitle">Report generated at ~~timestring()~~</p>
            </div>

        <!-- CSPro Data Processing -->
        <?
            numeric i=0;
            numeric SecondsInWeek = 60 * 60 * 24 * 7;
            numeric unsynced_count= countcases(CPI_CODEBOOK_DICT where synctime(CPI_CODEBOOK_DICT, "", uuid(CPI_CODEBOOK_DICT)) = notappl);
        ?>

        <?
            numeric Total_count = countcases(CPI_CODEBOOK_DICT);
            numeric partial_count = 0;
            numeric completed_count = 0;
            numeric tot_Refusal = 0;
            numeric not_found = 0;
            numeric others = 0;
            string partialCase, zdate, status;

            forcase CPI_CODEBOOK_DICT do
                if  !ispartial(CPI_CODEBOOK_DICT) and INTRODUCTION = 1 then
                inc(completed_count);
                endif;
            endfor;

            forcase CPI_CODEBOOK_DICT do
                if ispartial(CPI_CODEBOOK_DICT) then
                    inc(partial_count);
                    elseif INTERVIEW_RESULT = 3 then
                    inc(tot_Refusal);
                    elseif INTERVIEW_RESULT = 4 then
                    inc(not_found);
                    elseif INTERVIEW_RESULT = 98 then
                    inc(others);
                endif;
            endfor;
        ?>

            <!-- Modern Statistics Grid -->
            <div class="stats-grid">
                <div class="stat-card primary">
                    <div class="stat-icon">
                        <i class="bx bx-folder"></i>
                    </div>
                    <div class="stat-number">~~Total_count~~</div>
                    <div class="stat-label">Total Cases</div>
                </div>
                <div class="stat-card success">
                    <div class="stat-icon">
                        <i class="bx bx-check-circle"></i>
                    </div>
                    <div class="stat-number">~~completed_count~~</div>
                    <div class="stat-label">Completed</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="bx bx-time"></i>
                    </div>
                    <div class="stat-number">~~partial_count~~</div>
                    <div class="stat-label">Partial</div>
                </div>
                <div class="stat-card error">
                    <div class="stat-icon">
                        <i class="bx bx-x-circle"></i>
                    </div>
                    <div class="stat-number">~~tot_Refusal~~</div>
                    <div class="stat-label">Unavailable</div>
                </div>
                <div class="stat-card error">
                    <div class="stat-icon">
                        <i class="bx bx-search"></i>
                    </div>
                    <div class="stat-number">~~not_found~~</div>
                    <div class="stat-label">Not Found</div>
                </div>
                <div class="stat-card warning">
                    <div class="stat-icon">
                        <i class="bx bx-sync"></i>
                    </div>
                    <div class="stat-number">~~unsynced_count~~</div>
                    <div class="stat-label">Unsynced</div>
                </div>
            </div>

            <!-- Modern Performance Chart -->
            <div class="chart-container">
                <div class="chart-header">
                    <div>
                        <h3 class="chart-title">Daily Performance</h3>
                        <p class="chart-subtitle">Cases completed over time</p>
                    </div>
                </div>
                <div id="modernPerformanceChart" style="height: 250px;"></div>
            </div>

            <!-- Modern Case List -->
            <div class="case-list">
                <div class="case-list-header">
                    <h3 class="case-list-title">
                        <i class="bx bx-list-ul"></i>
                        Case Reports
                    </h3>
                    <span class="case-count">~~Total_count~~</span>
                </div>

            <!-- Completed Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  !ispartial(CPI_CODEBOOK_DICT) and INTERVIEW_RESULT = 1 then
                    status = "Completed";
            ?>
                <div class="case-item">
                    <div class="case-header">
                        <h4 class="case-name">~~strip(NAME_OF_OUTLET)~~</h4>
                        <span class="case-status status-completed">~~strip(status)~~</span>
                    </div>
                    <p class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </p>
                    <div class="case-meta">
                        <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View Case">
                            <i class="bx bx-show"></i>
                            View
                        </button>
                    </div>
                </div>
            <? endif;
            endfor; ?>

            <!-- Partial Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  ispartial(CPI_CODEBOOK_DICT) then
                    status = "Partial";
            ?>
            <div class="case-item">
                <div class="case-info">
                    <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                    <div class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </div>
                </div>
                <div>
                    <span class="case-status status-partial">~~strip(status)~~</span>
                    <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View">
                        <i class="bx bx-show"></i>
                    </button>
                </div>
            </div>
            <? endif;
            endfor; ?>

            <!-- Unavailable Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  !ispartial(CPI_CODEBOOK_DICT) and INTERVIEW_RESULT = 3 then
                    status = "Unavailable";
            ?>
            <div class="case-item">
                <div class="case-info">
                    <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                    <div class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </div>
                </div>
                <div>
                    <span class="case-status status-unavailable">~~strip(status)~~</span>
                    <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View">
                        <i class="bx bx-show"></i>
                    </button>
                </div>
            </div>
            <? endif;
            endfor; ?>

            <!-- Not Found Cases -->
            <? forcase CPI_CODEBOOK_DICT do
                inc(i);
                if  INTERVIEW_RESULT = 4 then
                    status = "Not Found";
            ?>
            <div class="case-item">
                <div class="case-info">
                    <div class="case-name">~~strip(NAME_OF_OUTLET)~~</div>
                    <div class="case-details">
                        EM: ~~Maketext(getlabel(ENUMERATION_MARKET_EM, ENUMERATION_MARKET_EM))~~ |
                        Date: ~~strip(INTERVIEW_START_DATE)~~
                    </div>
                </div>
                <div>
                    <span class="case-status status-not-found">~~strip(status)~~</span>
                    <button class="action-btn" onclick="CSPro.runLogicAsync('modify_case(~~i~~);')" title="View">
                        <i class="bx bx-show"></i>
                    </button>
                </div>
            </div>
            <? endif;
            endfor; ?>
            </div>

            <!-- Modern Floating Action Button -->
            <button class="fab" onclick="location.reload()" title="Refresh Dashboard">
                <i class="bx bx-refresh"></i>
            </button>

            <!-- Modern Toast Notification -->
            <div id="modernToast" class="modern-toast">
                <div class="toast-content">
                    <i class="bx bx-info-circle toast-icon"></i>
                    <div class="toast-text">
                        <div class="toast-title">Partial Cases Alert</div>
                        <div class="toast-message">You have ~~partial_count~~ partial cases that need attention</div>
                    </div>
                    <button class="toast-close" onclick="hideToast()">
                        <i class="bx bx-x"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Core JS -->
    <script src="./assets/vendor/libs/jquery/jquery.js"></script>
    <script src="./assets/vendor/libs/popper/popper.js"></script>
    <script src="./assets/vendor/js/bootstrap.js"></script>
    <script src="./assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="./assets/vendor/js/menu.js"></script>

    <!-- Vendors JS -->
    <script src="./assets/vendor/libs/apex-charts/apexcharts.js"></script>

    <!-- Main JS -->
    <script src="./assets/js/main.js"></script>

    <!-- Modern Mobile JavaScript -->
    <script>
        // Modern dashboard initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize modern components
            initModernDashboard();

            // Show toast notification if there are partial cases
            const partialCases = parseInt('~~partial_count~~') || 0;
            if (partialCases > 0) {
                showModernToast();
            }

            // Initialize modern performance chart
            initModernPerformanceChart();

            // Add modern touch feedback
            addModernTouchFeedback();

            // Initialize accessibility features
            initAccessibilityFeatures();
        });

        function initModernDashboard() {
            // Add loading states
            document.body.classList.add('dashboard-loaded');

            // Initialize intersection observer for animations
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe stat cards and case items
            document.querySelectorAll('.stat-card, .case-item').forEach(el => {
                el.style.opacity = '0';
                el.style.transform = 'translateY(20px)';
                el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(el);
            });
        }

        function showModernToast() {
            const toast = document.getElementById('modernToast');
            if (toast) {
                toast.style.display = 'block';
                toast.style.animation = 'slideInDown 0.5s ease';

                // Auto-hide after 8 seconds
                setTimeout(() => {
                    hideToast();
                }, 8000);
            }
        }

        function hideToast() {
            const toast = document.getElementById('modernToast');
            if (toast) {
                toast.style.animation = 'slideOutUp 0.5s ease';
                setTimeout(() => {
                    toast.style.display = 'none';
                }, 500);
            }
        }

        function initModernPerformanceChart() {
            const chartElement = document.querySelector('#modernPerformanceChart');
            if (!chartElement) return;

            // Modern chart configuration with enhanced styling
            const options = {
                series: [{
                    name: 'Completed Cases',
                    data: [12, 19, 15, 27, 22, 18, 25]
                }, {
                    name: 'Partial Cases',
                    data: [8, 12, 10, 15, 11, 9, 13]
                }],
                chart: {
                    type: 'area',
                    height: 250,
                    toolbar: { show: false },
                    background: 'transparent',
                    fontFamily: 'Inter, sans-serif'
                },
                colors: ['#0ea5e9', '#f59e0b'],
                fill: {
                    type: 'gradient',
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.4,
                        opacityTo: 0.1,
                        stops: [0, 90, 100]
                    }
                },
                stroke: {
                    curve: 'smooth',
                    width: 3
                },
                xaxis: {
                    categories: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                    labels: {
                        style: {
                            fontSize: '12px',
                            fontWeight: 500,
                            colors: '#64748b'
                        }
                    },
                    axisBorder: { show: false },
                    axisTicks: { show: false }
                },
                yaxis: {
                    labels: {
                        style: {
                            fontSize: '12px',
                            fontWeight: 500,
                            colors: '#64748b'
                        }
                    }
                },
                grid: {
                    borderColor: '#e2e8f0',
                    strokeDashArray: 3,
                    xaxis: { lines: { show: false } }
                },
                dataLabels: { enabled: false },
                legend: {
                    position: 'top',
                    horizontalAlign: 'left',
                    fontSize: '12px',
                    fontWeight: 500,
                    markers: { radius: 4 }
                },
                tooltip: {
                    theme: 'light',
                    style: { fontSize: '12px' },
                    x: { show: true },
                    marker: { show: true }
                }
            };

            const chart = new ApexCharts(chartElement, options);
            chart.render();
        }

        function addModernTouchFeedback() {
            // Enhanced haptic feedback for modern touch devices
            const interactiveElements = document.querySelectorAll('.action-btn, .fab, .stat-card, .case-item');

            interactiveElements.forEach(element => {
                // Touch start feedback
                element.addEventListener('touchstart', function(e) {
                    if (navigator.vibrate) {
                        navigator.vibrate(30); // Subtle vibration
                    }

                    // Visual feedback
                    this.style.transform = 'scale(0.98)';
                    this.style.transition = 'transform 0.1s ease';
                }, { passive: true });

                // Touch end feedback
                element.addEventListener('touchend', function(e) {
                    this.style.transform = '';
                }, { passive: true });

                // Touch cancel feedback
                element.addEventListener('touchcancel', function(e) {
                    this.style.transform = '';
                }, { passive: true });
            });
        }

        function initAccessibilityFeatures() {
            // Enhanced keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });

            document.addEventListener('mousedown', function() {
                document.body.classList.remove('keyboard-navigation');
            });

            // Screen reader announcements
            const announceUpdate = (message) => {
                const announcement = document.createElement('div');
                announcement.setAttribute('aria-live', 'polite');
                announcement.setAttribute('aria-atomic', 'true');
                announcement.className = 'sr-only';
                announcement.textContent = message;
                document.body.appendChild(announcement);

                setTimeout(() => {
                    document.body.removeChild(announcement);
                }, 1000);
            };

            // Announce when data updates
            window.announceUpdate = announceUpdate;
        }

        // Modern performance optimizations
        function optimizeModernPerformance() {
            // Intersection Observer for lazy loading
            const lazyElements = document.querySelectorAll('[data-lazy]');
            const lazyObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;
                        if (element.dataset.src) {
                            element.src = element.dataset.src;
                            element.removeAttribute('data-src');
                        }
                        lazyObserver.unobserve(element);
                    }
                });
            });

            lazyElements.forEach(el => lazyObserver.observe(el));

            // Optimize scroll performance
            let ticking = false;
            const optimizeScroll = () => {
                if (!ticking) {
                    requestAnimationFrame(() => {
                        // Scroll optimizations here
                        ticking = false;
                    });
                    ticking = true;
                }
            };

            window.addEventListener('scroll', optimizeScroll, { passive: true });
        }

        // Pull-to-refresh functionality
        function initPullToRefresh() {
            let startY = 0;
            let pullDistance = 0;
            const pullThreshold = 80;
            const refreshIndicator = document.createElement('div');
            refreshIndicator.className = 'pull-refresh-indicator';
            refreshIndicator.innerHTML = '<i class="bx bx-refresh"></i>';
            document.body.appendChild(refreshIndicator);

            document.addEventListener('touchstart', function(e) {
                startY = e.touches[0].pageY;
            }, { passive: true });

            document.addEventListener('touchmove', function(e) {
                if (window.scrollY === 0) {
                    pullDistance = e.touches[0].pageY - startY;
                    if (pullDistance > 0 && pullDistance < pullThreshold * 2) {
                        e.preventDefault();
                        const progress = Math.min(pullDistance / pullThreshold, 1);
                        refreshIndicator.style.transform = `translateY(${pullDistance * 0.5}px) rotate(${progress * 360}deg)`;
                        refreshIndicator.style.opacity = progress;
                        document.body.style.transform = `translateY(${pullDistance * 0.3}px)`;
                    }
                }
            });

            document.addEventListener('touchend', function() {
                if (pullDistance > pullThreshold && window.scrollY === 0) {
                    // Trigger refresh
                    refreshIndicator.style.animation = 'spin 1s linear infinite';
                    setTimeout(() => {
                        location.reload();
                    }, 500);
                } else {
                    // Reset
                    document.body.style.transform = '';
                    refreshIndicator.style.transform = '';
                    refreshIndicator.style.opacity = '0';
                }
                pullDistance = 0;
            }, { passive: true });
        }

        // Initialize all modern optimizations
        optimizeModernPerformance();
        initPullToRefresh();
    </script>
</body>
</html>
